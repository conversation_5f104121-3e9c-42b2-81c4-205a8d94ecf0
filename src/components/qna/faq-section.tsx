'use client';
import { getCategoryCodes } from '@/action/category-action';
import { getFaqList } from '@/action/faq-action';
import { CategoryCode } from '@/types/category-code';
import { Faq, FaqResponse } from '@/types/faq';
import { useLocale } from 'next-intl';

import { useEffect, useState } from 'react';

export default function FaqSection() {
  const locale = useLocale();
  const [categoryCodes, setCategoryCodes] = useState<CategoryCode[]>([]);
  const [faqList, setFaqList] = useState<Faq[]>([]);
  useEffect(() => {
    const init = async () => {
      const categoryCodeRequest = { startNum: 0, scaleNum: 0, siteMenu: 'FAQ' };
      const categoryCodeResponse = await getCategoryCodes(categoryCodeRequest);
      if (categoryCodeResponse.status == 200) {
        setCategoryCodes(categoryCodeResponse.codes);
      } else {
        setCategoryCodes([]);
      }

      const faqRequest = { locale: locale, keyword: '', categoryCode: '' };
      const faqResponse = await getFaqList(faqRequest);

      if (faqResponse.status == 200) {
        setFaqList(faqResponse.faqs.slice(0, 3));
      } else {
        setFaqList([]);
      }
    };
    init();
  }, []);

  return (
    <>
      <div className="grid gap-5 lg:gap-5">
        <div className="card pb-2.5">
          <div className="card-body">
            <div className="grid gap-3">
              <div className="flex items-center lg:gap-3">
                {categoryCodes.map((item, index) => (
                  <button key={index} className="btn btn-outline btn-secondary">
                    {item.type}
                  </button>
                ))}
              </div>

              <div className="grid grid-cols-3 gap-5">
                {faqList.map((item, index) => (
                  <div className="card" key={index}>
                    <div className="card-body">
                      <pre className="text-wrap">{item.questions}</pre>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
