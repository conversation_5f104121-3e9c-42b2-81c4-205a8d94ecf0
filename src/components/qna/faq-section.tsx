'use client';
import { getCategoryCodes } from '@/action/category-action';
import { getFaqList } from '@/action/faq-action';
import { CategoryCode } from '@/types/category-code';
import { Faq, FaqResponse } from '@/types/faq';
import { useLocale } from 'next-intl';

import { useCallback, useEffect, useState } from 'react';

export default function FaqSection() {
  const locale = useLocale();
  const [categoryCodes, setCategoryCodes] = useState<CategoryCode[]>([]);
  const [categoryCode, setCategoryCode] = useState<string>('');
  const [faqList, setFaqList] = useState<Faq[]>([]);
  useEffect(() => {
    const init = async () => {
      const categoryCodeRequest = { startNum: 0, scaleNum: 0, siteMenu: 'FAQ' };
      const categoryCodeResponse = await getCategoryCodes(categoryCodeRequest);
      if (categoryCodeResponse.status == 200) {
        setCategoryCodes(categoryCodeResponse.codes);
      } else {
        setCategoryCodes([]);
      }
    };
    init();
    getFaqsData('');
  }, []);

  const getFaqsData = async (categoryCode: string = '') => {
    setCategoryCode(categoryCode);
    const faqRequest = { locale: locale, keyword: '', categoryCode: categoryCode };
    const faqResponse = await getFaqList(faqRequest);

    if (faqResponse.status == 200) {
      setFaqList(faqResponse.faqs.slice(0, 3));
    } else {
      setFaqList([]);
    }
  };

  return (
    <>
      <div className="grid gap-5 lg:gap-5">
        <div className="card pb-2.5">
          <div className="card-body">
            <div className="grid gap-3">
              <div className="flex items-center lg:gap-3">
                {categoryCodes.map((item, index) => (
                  <button
                    key={index}
                    className={`btn btn-outline ${categoryCode === item.code ? 'btn-primary' : 'btn-secondary'}`}
                    onClick={() => getFaqsData(item.code)}
                  >
                    {item.type}
                  </button>
                ))}
              </div>

              <div className="grid grid-cols-1 gap-5 lg:grid-cols-3">
                {faqList.map((item, index) => (
                  <div className="card hover:cursor-pointer" key={index}>
                    <div className="card-body flex flex-col gap-2.5">
                      <div className="text-wrap text-base font-semibold">Q. {item.questions}</div>
                      <div
                        className="max-h-[200px] overflow-y-auto text-sm font-normal text-gray-600"
                        dangerouslySetInnerHTML={{ __html: item.answers.replaceAll('\n', '<br/>') }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
